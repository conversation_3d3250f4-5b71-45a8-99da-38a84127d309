{"hash": "c95c95f5", "configHash": "8326b104", "lockfileHash": "b7ec7981", "browserHash": "f5a1cba2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f6c5e896", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f419b744", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "32f3a094", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "88f1d9a3", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "3e5902f3", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1002cf80", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5dbc13ad", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "8e561a1e", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9331d62d", "needsInterop": false}}, "chunks": {"chunk-SB5BK2J2": {"file": "chunk-SB5BK2J2.js"}, "chunk-N6MYFXC3": {"file": "chunk-N6MYFXC3.js"}}}